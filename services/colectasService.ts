import { supabase } from '@/lib/supabase';
import { Colecta, Contribution, ColectaStatus, ContributionStatus } from '@/types';

export interface ColectaData {
  motivo: string;
  descripcion: string;
  fechaFin: string;
  baseAmount: number;
  targetAmount: number;
  accountInfo: string;
  titularCuenta: string;
  cuitCuil: string;
  aliasCbu: string;
}

export interface ContributionData {
  amount: number;
  paymentMethod: string;
  paymentReference?: string;
  proofOfPaymentUrl?: string;
}

export const ColectasService = {
  /**
   * Get colectas for a group
   */
  getColectasByGroupId: async (groupId: string): Promise<Colecta[]> => {
    const { data, error } = await supabase
      .from('colectas')
      .select(`
        id,
        group_id,
        motivo,
        descripcion,
        fecha_fin,
        base_amount,
        target_amount,
        total_collected,
        total_contributions,
        account_info,
        titular_cuenta,
        cuit_cuil,
        alias_cbu,
        status,
        created_by,
        created_at
      `)
      .eq('group_id', groupId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    // Map the data to the Colecta type
    return data.map(colecta => ({
      id: colecta.id,
      groupId: colecta.group_id,
      motivo: colecta.motivo,
      descripcion: colecta.descripcion,
      fechaFin: colecta.fecha_fin,
      baseAmount: colecta.base_amount,
      targetAmount: colecta.target_amount,
      totalCollected: colecta.total_collected,
      totalContributions: colecta.total_contributions,
      accountInfo: colecta.account_info,
      titularCuenta: colecta.titular_cuenta,
      cuitCuil: colecta.cuit_cuil,
      aliasCbu: colecta.alias_cbu,
      status: colecta.status,
      createdBy: colecta.created_by,
      createdAt: colecta.created_at,
    }));
  },

  /**
   * Get a colecta by ID
   */
  getColectaById: async (colectaId: string): Promise<Colecta | null> => {
    const { data, error } = await supabase
      .from('colectas')
      .select(`
        id,
        group_id,
        motivo,
        descripcion,
        fecha_fin,
        base_amount,
        total_collected,
        total_contributions,
        account_info,
        titular_cuenta,
        cuit_cuil,
        alias_cbu,
        status,
        created_by,
        created_at
      `)
      .eq('id', colectaId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Colecta not found
      }
      throw new Error(error.message);
    }

    // Map the data to the Colecta type
    return {
      id: data.id,
      groupId: data.group_id,
      motivo: data.motivo,
      descripcion: data.descripcion,
      fechaFin: data.fecha_fin,
      baseAmount: data.base_amount,
      totalCollected: data.total_collected,
      totalContributions: data.total_contributions,
      accountInfo: data.account_info,
      titularCuenta: data.titular_cuenta,
      cuitCuil: data.cuit_cuil,
      aliasCbu: data.alias_cbu,
      status: data.status,
      createdBy: data.created_by,
      createdAt: data.created_at,
    };
  },

  /**
   * Create a new colecta
   */
  createColecta: async (
    groupId: string,
    userId: string,
    colectaData: ColectaData
  ): Promise<Colecta> => {
    const { motivo, descripcion, fechaFin, baseAmount, targetAmount, accountInfo, titularCuenta, cuitCuil, aliasCbu } = colectaData;

    // Insert new colecta
    const { data, error } = await supabase
      .from('colectas')
      .insert({
        group_id: groupId,
        motivo,
        descripcion,
        fecha_fin: fechaFin,
        base_amount: baseAmount,
        target_amount: targetAmount,
        total_collected: 0,
        total_contributions: 0,
        account_info: accountInfo,
        titular_cuenta: titularCuenta,
        cuit_cuil: cuitCuil,
        alias_cbu: aliasCbu,
        status: 'ACTIVA',
        created_by: userId,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Return the new colecta
    return {
      id: data.id,
      groupId: data.group_id,
      motivo: data.motivo,
      descripcion: data.descripcion,
      fechaFin: data.fecha_fin,
      baseAmount: data.base_amount,
      targetAmount: data.target_amount,
      totalCollected: data.total_collected,
      totalContributions: data.total_contributions,
      accountInfo: data.account_info,
      titularCuenta: data.titular_cuenta,
      cuitCuil: data.cuit_cuil,
      aliasCbu: data.alias_cbu,
      status: data.status,
      createdBy: data.created_by,
      createdAt: data.created_at,
    };
  },

  /**
   * Update a colecta's status
   */
  updateColectaStatus: async (colectaId: string, status: ColectaStatus): Promise<void> => {
    const { error } = await supabase
      .from('colectas')
      .update({ status })
      .eq('id', colectaId);

    if (error) {
      throw new Error(error.message);
    }
  },

  /**
   * Get contributions for multiple colectas
   */
  getContributionsByColectaIds: async (colectaIds: string[]): Promise<Contribution[]> => {
    if (colectaIds.length === 0) {
      return [];
    }

    console.log('Fetching contributions for colectas:', colectaIds);

    try {
      const { data, error } = await supabase
        .from('contributions')
        .select(`
          id,
          colecta_id,
          user_id,
          kid_id,
          amount,
          payment_method,
          payment_reference,
          proof_of_payment_url,
          notes,
          status,
          created_at
        `)
        .in('colecta_id', colectaIds)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching contributions:', error);
        throw new Error(error.message);
      }

      console.log(`Fetched ${data?.length || 0} contributions`);

      // Map the data to the Contribution type
      return (data || []).map(contribution => ({
        id: contribution.id,
        colectaId: contribution.colecta_id,
        userId: contribution.user_id,
        kidId: contribution.kid_id,
        amount: contribution.amount,
        paymentMethod: contribution.payment_method,
        paymentReference: contribution.payment_reference,
        proofOfPaymentUrl: contribution.proof_of_payment_url,
        notes: contribution.notes,
        status: contribution.status,
        createdAt: contribution.created_at,
      }));
    } catch (error) {
      console.error('Error in getContributionsByColectaIds:', error);
      // Return empty array instead of throwing to prevent app crashes
      return [];
    }
  },

  /**
   * Create a new contribution
   */
  createContribution: async (
    colectaId: string,
    kidId: string,
    userId: string,
    amount: number,
    paymentMethod: string,
    paymentReference?: string,
    proofOfPaymentUrl?: string,
    notes?: string
  ): Promise<Contribution> => {
    try {
      console.log('Creating contribution for colecta:', colectaId);

      // Prepare the insert data
      const insertData: any = {
        colecta_id: colectaId,
        kid_id: kidId,
        user_id: userId,
        amount,
        payment_method: paymentMethod,
        payment_reference: paymentReference,
        proof_of_payment_url: proofOfPaymentUrl,
        status: 'REGISTRADA',
      };

      // Only add notes if it's provided (to handle cases where column doesn't exist yet)
      if (notes) {
        insertData.notes = notes;
      }

      // Insert new contribution
      const { data, error } = await supabase
        .from('contributions')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error('Error creating contribution:', error);
        console.error('Insert data was:', insertData);
        throw new Error(`Error al crear contribución: ${error.message}`);
      }

      console.log('Contribution created:', data);

      // Get current colecta values
      const { data: colectaData, error: colectaError } = await supabase
        .from('colectas')
        .select('total_collected, total_contributions')
        .eq('id', colectaId)
        .single();

      if (colectaError) {
        console.error('Error fetching colecta data:', colectaError);
      } else {
        // Update colecta totals directly
        const { error: updateError } = await supabase
          .from('colectas')
          .update({
            total_collected: colectaData.total_collected + amount,
            total_contributions: colectaData.total_contributions + 1,
          })
          .eq('id', colectaId);

        if (updateError) {
          console.error('Error updating colecta totals:', updateError);
          // Don't throw here, just log the error
        }
      }

      // Map the data to the Contribution type
      return {
        id: data.id,
        colectaId: data.colecta_id,
        userId: data.user_id,
        kidId: data.kid_id,
        amount: data.amount,
        paymentMethod: data.payment_method,
        paymentReference: data.payment_reference,
        proofOfPaymentUrl: data.proof_of_payment_url,
        notes: data.notes,
        status: data.status,
        createdAt: data.created_at,
      };
    } catch (error) {
      console.error('Error in createContribution:', error);
      throw error;
    }
  },

  /**
   * Get contributions for a colecta
   */
  getContributionsByColectaId: async (colectaId: string): Promise<Contribution[]> => {
    try {
      const { data, error } = await supabase
        .from('contributions')
        .select(`
          id,
          colecta_id,
          user_id,
          kid_id,
          amount,
          payment_method,
          payment_reference,
          proof_of_payment_url,
          notes,
          status,
          created_at
        `)
        .eq('colecta_id', colectaId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching contributions for colecta:', error);
        throw new Error(error.message);
      }

      console.log(`Fetched ${data?.length || 0} contributions for colecta ${colectaId}`);

      // Map the data to the Contribution type
      return (data || []).map(contribution => ({
        id: contribution.id,
        colectaId: contribution.colecta_id,
        userId: contribution.user_id,
        kidId: contribution.kid_id,
        amount: contribution.amount,
        paymentMethod: contribution.payment_method,
        paymentReference: contribution.payment_reference,
        proofOfPaymentUrl: contribution.proof_of_payment_url,
        notes: contribution.notes,
        status: contribution.status,
        createdAt: contribution.created_at,
      }));
    } catch (error) {
      console.error('Error in getContributionsByColectaId:', error);
      // Return empty array instead of throwing to prevent app crashes
      return [];
    }
  },

  /**
   * Contribute to a colecta
   */
  contributeToColecta: async (
    colectaId: string,
    userId: string,
    kidId: string,
    contributionData: ContributionData
  ): Promise<Contribution> => {
    const { amount, paymentMethod, paymentReference, proofOfPaymentUrl } = contributionData;

    // Insert new contribution
    const { data, error } = await supabase
      .from('contributions')
      .insert({
        colecta_id: colectaId,
        user_id: userId,
        kid_id: kidId,
        amount,
        payment_method: paymentMethod,
        payment_reference: paymentReference,
        proof_of_payment_url: proofOfPaymentUrl,
        status: 'REGISTRADA',
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Update colecta totals
    try {
      // Get current colecta values
      const { data: colectaData, error: colectaError } = await supabase
        .from('colectas')
        .select('total_collected, total_contributions')
        .eq('id', colectaId)
        .single();

      if (colectaError) {
        console.error('Error fetching colecta data:', colectaError);
      } else {
        // Update colecta totals directly
        const { error: updateError } = await supabase
          .from('colectas')
          .update({
            total_collected: colectaData.total_collected + amount,
            total_contributions: colectaData.total_contributions + 1,
          })
          .eq('id', colectaId);

        if (updateError) {
          console.error('Error updating colecta totals:', updateError);
        }
      }
    } catch (error) {
      console.error('Error updating colecta totals:', error);
      // Don't throw here, just log the error
    }

    // Return the new contribution
    return {
      id: data.id,
      colectaId: data.colecta_id,
      userId: data.user_id,
      kidId: data.kid_id,
      amount: data.amount,
      paymentMethod: data.payment_method,
      paymentReference: data.payment_reference,
      proofOfPaymentUrl: data.proof_of_payment_url,
      status: data.status,
      createdAt: data.created_at,
    };
  },

  /**
   * Update a contribution's status
   */
  updateContributionStatus: async (contributionId: string, status: string): Promise<void> => {
    const { error } = await supabase
      .from('contributions')
      .update({ status })
      .eq('id', contributionId);

    if (error) {
      throw new Error(error.message);
    }
  },
};
