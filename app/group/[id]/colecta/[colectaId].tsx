import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, TouchableOpacity, Image, RefreshControl, Modal, Clipboard } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { DollarSign, Calendar, AtSign, Upload, CheckCircle, XCircle, AlertCircle, User, CreditCard, Landmark, X, ChevronDown, ChevronUp, Copy } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { Input } from '@/components/Input';
import { colors } from '@/constants/colors';
import { useColectasStore } from '@/store/colectasStore';
import { useGroupsStore } from '@/store/groupsStore';
import { useKidsStore } from '@/store/kidsStore';
import { useAuthStore } from '@/store/authStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { ContributionItem } from '@/components/ContributionItem';
import { Badge } from '@/components/Badge';

type ContributionStatus = 'REGISTRADA' | 'CONFIRMADA' | 'RECHAZADA';

interface ContributionItemProps {
  kidName: string;
  amount: number;
  status: ContributionStatus | string;
  date?: string;
  createdAt?: string;
  proofUrl?: string;
  onProofPress?: () => void;
}

export default function ColectaDetailScreen() {
  const { id: groupId, colectaId } = useLocalSearchParams<{ id: string; colectaId: string }>();
  const { colectas, contributions, getColectasByGroupId, getContributionsByColectaId, contributeToColecta, updateColectaStatus, isLoading } = useColectasStore();
  const { isReferente, getMembershipsByGroupId } = useGroupsStore();
  const { kids, groupKids, getKidsByIds } = useKidsStore();
  const { currentUser, users, fetchUsersByIds } = useAuthStore();

  const [refreshing, setRefreshing] = useState(false);
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'status'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const colecta = colectas.find(c => c.id === colectaId);
  const userIsReferente = isReferente(groupId);
  const groupMemberships = getMembershipsByGroupId(groupId);

  // Get actual contributions count for this colecta
  const actualContributionsCount = contributions.filter(c => c.colectaId === colectaId).length;

  // Get user's kids in this group
  const userKidsInGroup = kids.filter(kid =>
    groupMemberships.some(m => m.kidId === kid.id)
  );

  // Check if all user's kids in the group have contributed
  const allKidsContributed = userKidsInGroup.length > 0 && userKidsInGroup.every(kid => {
    return contributions.some(c =>
      c.colectaId === colectaId &&
      c.kidId === kid.id &&
      c.status === 'REGISTRADA'
    );
  });

  /**
   * Load data for the colecta
   * @param forceRefresh Whether to force a refresh of all data
   */
  const loadColectaData = async (forceRefresh = false) => {
    if (!groupId || !colectaId) return;

    console.log(`ColectaDetailScreen: ${forceRefresh ? 'Force refreshing' : 'Loading'} data`);

    try {
      // Only load colecta data if we don't already have it or if forcing refresh
      const hasColecta = !forceRefresh && colectas?.some(c => c.id === colectaId);
      if (!hasColecta) {
        console.log('ColectaDetailScreen: Fetching colecta data');
        await getColectasByGroupId(groupId);
      } else {
        console.log('ColectaDetailScreen: Using cached colecta data');
      }

      // Check if getContributionsByColectaId is available and load contributions
      if (typeof getContributionsByColectaId === 'function') {
        // Always fetch the latest contributions for this colecta
        console.log('ColectaDetailScreen: Fetching contributions data');
        await getContributionsByColectaId(colectaId);
      } else {
        console.warn('getContributionsByColectaId is not available yet. Please restart the app.');
      }

      // Get all kid IDs from the contributions to ensure we load all kids in the group
      const contributionKidIds = contributions
        .filter(c => c.colectaId === colectaId)
        .map(c => c.kidId);

      if (contributionKidIds.length > 0) {
        console.log('ColectaDetailScreen: Fetching kids data for contributions');
        // Always fetch the latest kid data for all contributions
        await getKidsByIds(contributionKidIds, true);
      }

      // Get unique user IDs from contributions to fetch user data
      const contributionUserIds = [...new Set(
        contributions
          .filter(c => c.colectaId === colectaId)
          .map(c => c.userId)
          .filter(Boolean)
      )];

      if (contributionUserIds.length > 0) {
        console.log('ColectaDetailScreen: Fetching users data for contributions');
        // Always fetch the latest user data for all contributions
        await fetchUsersByIds(contributionUserIds);
      }
    } catch (error) {
      console.error('ColectaDetailScreen: Error loading data:', error);
    }
  };

  // Handle pull-to-refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await loadColectaData(true); // Force refresh
    setRefreshing(false);
  };

  // Load data only when component mounts or IDs change, not continuously
  useEffect(() => {
    if (groupId && colectaId) {
      loadColectaData();
    }
  }, [groupId, colectaId]); // Only depend on IDs to prevent continuous fetching

  // Debug log for contributions and kids
  useEffect(() => {
    if (contributions.length > 0 && colectaId) {
      const colectaContributions = contributions.filter(c => c.colectaId === colectaId);
      console.log(`ColectaDetailScreen: ${colectaContributions.length} contributions for this colecta`);
      console.log(`ColectaDetailScreen: User kids: ${kids.length}, Group kids: ${groupKids.length}`);

      // Log each contribution and the associated kid
      colectaContributions.forEach(contribution => {
        const kid = kids.find(k => k.id === contribution.kidId) ||
                   groupKids.find(k => k.id === contribution.kidId);
        console.log(`Contribution: ${contribution.id}, Kid: ${kid?.fullName || 'Unknown'} (${contribution.kidId})`);
      });
    }
  }, [contributions, kids, groupKids, colectaId]);



  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Fecha inválida';

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  };

  const getDaysRemaining = (dateString: string) => {
    const targetDate = new Date(dateString);
    const today = new Date();

    // Reset time to start of day for accurate day calculation
    today.setHours(0, 0, 0, 0);
    targetDate.setHours(0, 0, 0, 0);

    const diffTime = targetDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return 'Vencida';
    if (diffDays === 0) return 'Hoy';
    if (diffDays === 1) return 'Mañana';
    return `Faltan ${diffDays} días`;
  };

  const copyToClipboard = (text: string) => {
    try {
      Clipboard.setString(text);
      Alert.alert('Copiado', 'Alias/CBU copiado al portapapeles');
    } catch (error) {
      Alert.alert('Error', 'No se pudo copiar al portapapeles');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-AR', {
      style: 'currency',
      currency: 'ARS',
      minimumFractionDigits: 0,
    }).format(amount);
  };



  const handleUpdateStatus = async (status: 'ACTIVA' | 'FINALIZADA' | 'CANCELADA') => {
    if (!userIsReferente) {
      Alert.alert('Error', 'Solo los referentes pueden cambiar el estado de la colecta');
      return;
    }

    const statusText = status === 'FINALIZADA' ? 'finalizada' : status === 'CANCELADA' ? 'cancelada' : 'activa';

    Alert.alert(
      'Cambiar Estado',
      `¿Estás seguro que deseas marcar esta colecta como ${statusText}?`,
      [
        {
          text: 'Cancelar',
          style: 'cancel',
        },
        {
          text: 'Confirmar',
          onPress: async () => {
            try {
              await updateColectaStatus(colectaId, status);
              Alert.alert('Estado Actualizado', `La colecta ha sido marcada como ${statusText}.`);
            } catch (error) {
              Alert.alert('Error', 'No se pudo actualizar el estado de la colecta');
            }
          },
        },
      ]
    );
  };

  const getStatusIcon = () => {
    if (!colecta) return null;

    switch (colecta.status) {
      case 'FINALIZADA':
        return <CheckCircle size={20} color={colors.success} />;
      case 'CANCELADA':
        return <XCircle size={20} color={colors.error} />;
      default: // ACTIVA
        return <AlertCircle size={20} color={colors.warning} />;
    }
  };

  const getStatusText = () => {
    if (!colecta) return '';

    switch (colecta.status) {
      case 'FINALIZADA':
        return 'Finalizada';
      case 'CANCELADA':
        return 'Cancelada';
      default: // ACTIVA
        return 'Activa';
    }
  };

  const getStatusColor = (status: ContributionStatus) => {
    switch (status) {
      case 'REGISTRADA':
        return colors.warning;
      case 'CONFIRMADA':
        return colors.success;
      case 'RECHAZADA':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getProgressPercentage = () => {
    if (!colecta || !colecta.targetAmount) return 0;
    return Math.min(100, ((colecta.totalCollected || 0) / colecta.targetAmount) * 100);
  };

  const getSortedContributions = () => {
    const colectaContributions = contributions.filter(c => c.colectaId === colectaId);

    return colectaContributions.sort((a, b) => {
      if (sortBy === 'date') {
        return sortOrder === 'desc'
          ? new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          : new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      }
      if (sortBy === 'amount') {
        return sortOrder === 'desc' ? b.amount - a.amount : a.amount - b.amount;
      }
      // status sorting
      return sortOrder === 'desc'
        ? b.status.localeCompare(a.status)
        : a.status.localeCompare(b.status);
    });
  };

  const handleSortChange = (newSortBy: 'date' | 'amount' | 'status') => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('desc');
    }
  };

  const getUserName = (userId: string) => {
    // If it's the current user, use their data
    if (currentUser?.id === userId) {
      return `${currentUser.firstName} ${currentUser.lastName}`;
    }

    // Look for the user in the users array
    const user = users.find(u => u.id === userId);
    if (user) {
      return `${user.firstName} ${user.lastName}`;
    }

    // Fallback
    return 'Usuario';
  };



  if (!colecta) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: 'Detalle de Colecta' }} />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>Colecta no encontrada</Text>
          <Button
            title="Volver"
            onPress={() => router.back()}
            variant="outline"
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Detalle de Colecta',
          headerBackTitle: 'Atrás',
        }}
      />

      <AnimatedTransition>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }>

          <Card variant="elevated" style={styles.colectaCard}>
            <View style={styles.colectaHeader}>
              <Text style={styles.colectaTitle}>{colecta.motivo}</Text>
              <View style={styles.statusContainer}>
                {getStatusIcon()}
                <Text style={[styles.statusText, { color: getStatusColor(colecta.status as ContributionStatus) }]}>
                  {getStatusText()}
                </Text>
                {allKidsContributed && (
                  <View style={styles.allKidsContributedBadge}>
                    <CheckCircle size={16} color={colors.success} />
                    <Text style={[styles.allKidsContributedText, { color: colors.success }]}>
                      Todos tus Qids han contribuido
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {colecta.descripcion && (
              <Text style={styles.colectaDescription}>{colecta.descripcion}</Text>
            )}

            <View style={styles.colectaDetails}>
              <View style={styles.detailItem}>
                <Calendar size={16} color={colors.textSecondary} />
                <View style={styles.dateContainer}>
                  <Text style={styles.detailText}>
                    Fecha límite: {formatDate(colecta.fechaFin)}
                  </Text>
                  <Text style={styles.daysRemainingText}>
                    ({getDaysRemaining(colecta.fechaFin)})
                  </Text>
                </View>
              </View>

              <View style={styles.detailItem}>
                <DollarSign size={16} color={colors.textSecondary} />
                <Text style={styles.detailText}>
                  Monto sugerido: {formatCurrency(colecta.baseAmount)}
                </Text>
              </View>

              <Text style={styles.paymentSectionTitle}>Información de pago</Text>

              <Card variant="outlined" style={styles.paymentCard}>
                <View style={styles.detailItem}>
                  <AtSign size={16} color={colors.textSecondary} />
                  <Text style={styles.detailText}>
                    Información: {colecta.accountInfo || 'No especificado'}
                  </Text>
                </View>

                <View style={styles.detailItem}>
                  <User size={16} color={colors.textSecondary} />
                  <Text style={styles.detailText}>
                    Titular: {colecta.titularCuenta || 'No especificado'}
                  </Text>
                </View>

                <View style={styles.detailItem}>
                  <CreditCard size={16} color={colors.textSecondary} />
                  <Text style={styles.detailText}>
                    CUIT/CUIL: {colecta.cuitCuil || 'No especificado'}
                  </Text>
                </View>

                <View style={styles.detailItem}>
                  <Landmark size={16} color={colors.textSecondary} />
                  <Text style={styles.detailText}>
                    Alias/CBU: {colecta.aliasCbu || 'No especificado'}
                  </Text>
                  {colecta.aliasCbu && (
                    <TouchableOpacity
                      style={styles.copyButton}
                      onPress={() => copyToClipboard(colecta.aliasCbu)}
                    >
                      <Copy size={16} color={colors.primary} />
                    </TouchableOpacity>
                  )}
                </View>
              </Card>
            </View>
          </Card>

          {/* Progress Section */}
          <View style={styles.progressContainer}>
            <View style={styles.progressSection}>
              <Text style={styles.progressLabel}>
                Recaudado:
              </Text>
              <Text style={styles.progressAmounts}>
                {formatCurrency(colecta.totalCollected || 0)} de {formatCurrency(colecta.targetAmount || 0)}
              </Text>

              <View style={styles.progressBarContainer}>
                <View
                  style={[
                    styles.progressBar,
                    { width: `${getProgressPercentage()}%` }
                  ]}
                />
              </View>

              <Text style={styles.contributionsText}>
                {actualContributionsCount} contribuciones
              </Text>
            </View>
          </View>

          {colecta.status !== 'CANCELADA' && (
            <View style={styles.section}>
              <Button
                title="Registrar Contribución"
                onPress={() => router.push(`/group/${groupId}/colecta/${colectaId}/contribute`)}
                style={styles.contributeButton}
              />
            </View>
          )}



          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Contribuciones</Text>
              <View style={styles.sortContainer}>
                <TouchableOpacity
                  style={styles.sortButton}
                  onPress={() => handleSortChange('date')}
                >
                  <Text style={[
                    styles.sortButtonText,
                    sortBy === 'date' && styles.activeSortButtonText
                  ]}>
                    Fecha {sortBy === 'date' && (sortOrder === 'desc' ? '↓' : '↑')}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.sortButton}
                  onPress={() => handleSortChange('amount')}
                >
                  <Text style={[
                    styles.sortButtonText,
                    sortBy === 'amount' && styles.activeSortButtonText
                  ]}>
                    Monto {sortBy === 'amount' && (sortOrder === 'desc' ? '↓' : '↑')}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.sortButton}
                  onPress={() => handleSortChange('status')}
                >
                  <Text style={[
                    styles.sortButtonText,
                    sortBy === 'status' && styles.activeSortButtonText
                  ]}>
                    Estado {sortBy === 'status' && (sortOrder === 'desc' ? '↓' : '↑')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {getSortedContributions().length > 0 ? (
              getSortedContributions().map(contribution => {
                const kid = kids.find(k => k.id === contribution.kidId) ||
                           groupKids.find(k => k.id === contribution.kidId);
                const parent = getUserName(contribution.userId);
                return (
                  <ContributionItem
                    key={contribution.id}
                    kidName={kid?.fullName || 'Desconocido'}
                    parentName={parent}
                    amount={contribution.amount}
                    date={contribution.createdAt}
                    status={contribution.status}
                    proofUrl={contribution.proofOfPaymentUrl}
                    onProofPress={() => {
                      if (contribution.proofOfPaymentUrl) {
                        setPreviewImageUrl(contribution.proofOfPaymentUrl);
                        setShowImagePreview(true);
                      }
                    }}
                  />
                );
              })
            ) : (
              <Card variant="outlined" style={styles.emptyCard}>
                <Text style={styles.emptyText}>
                  No hay contribuciones registradas
                </Text>
              </Card>
            )}
          </View>
        </ScrollView>
      </AnimatedTransition>

      <Modal
        visible={showImagePreview}
        transparent={true}
        animationType="fade"
        onRequestClose={() => {
          setShowImagePreview(false);
          setPreviewImageUrl(null);
        }}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => {
                setShowImagePreview(false);
                setPreviewImageUrl(null);
              }}
            >
              <X size={24} color={colors.text} />
            </TouchableOpacity>
            <Image
              source={previewImageUrl ? { uri: previewImageUrl } : undefined}
              style={styles.modalImage}
              resizeMode="contain"
            />
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
    marginTop: 4,
    marginBottom: 16,
  },
  backButton: {
    minWidth: 120,
  },
  scrollContent: {
    padding: 16,
  },
  colectaCard: {
    marginBottom: 16,
    padding: 16,
  },
  colectaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  colectaTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
    marginRight: 8,
  },
  colectaDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  colectaDetails: {
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  daysRemainingText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  copyButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: colors.primaryLight,
    marginLeft: 8,
  },
  paymentSectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginTop: 12,
    marginBottom: 6,
  },
  paymentCard: {
    padding: 12,
    marginBottom: 0,
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressSection: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: colors.background,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 4,
  },
  progressAmounts: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  contributionsText: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: colors.secondary,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressBar: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  kidSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  kidOption: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.cardBackground,
  },
  selectedKidOption: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight,
  },
  kidOptionText: {
    fontSize: 14,
    color: colors.text,
  },
  selectedKidOptionText: {
    color: colors.primary,
    fontWeight: '500',
  },
  uploadContainer: {
    marginBottom: 16,
  },
  uploadPlaceholder: {
    height: 80,
    borderWidth: 1,
    borderColor: colors.border,
    borderStyle: 'dashed',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  uploadText: {
    marginTop: 8,
    fontSize: 14,
    color: colors.textSecondary,
  },
  proofImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
  },
  contributeButton: {
    marginTop: 8,
  },
  emptyCard: {
    padding: 16,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sortContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  sortButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    backgroundColor: colors.secondary,
  },
  sortButtonText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  activeSortButtonText: {
    color: colors.primary,
    fontWeight: '500',
  },
  proofImageContainer: {
    position: 'relative',
    width: '100%',
    height: 120,
    borderRadius: 8,
    overflow: 'hidden',
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    height: '80%',
    position: 'relative',
  },
  modalImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  closeButton: {
    position: 'absolute',
    top: -40,
    right: 0,
    zIndex: 1,
    padding: 8,
  },
  allKidsContributedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 4,
  },
  allKidsContributedText: {
    fontSize: 12,
    fontWeight: '500',
  },
});