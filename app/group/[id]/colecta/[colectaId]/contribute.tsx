import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, TouchableOpacity, Image, KeyboardAvoidingView, Platform } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { DollarSign, Upload, X } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { Input } from '@/components/Input';
import { colors } from '@/constants/colors';
import { useColectasStore } from '@/store/colectasStore';
import { useGroupsStore } from '@/store/groupsStore';
import { useKidsStore } from '@/store/kidsStore';
import { useAuthStore } from '@/store/authStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';

export default function ContributeScreen() {
  const { id: groupId, colectaId } = useLocalSearchParams<{ id: string; colectaId: string }>();
  const { colectas, contributeToColecta } = useColectasStore();
  const { getMembershipsByGroupId } = useGroupsStore();
  const { kids } = useKidsStore();
  const { currentUser } = useAuthStore();

  const [selectedKidId, setSelectedKidId] = useState<string | null>(null);
  const [amount, setAmount] = useState('');
  const [proofImage, setProofImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const colecta = colectas.find(c => c.id === colectaId);
  const memberships = getMembershipsByGroupId(groupId as string);
  const userMembership = memberships.find(m => m.userId === currentUser?.id);
  const userKidsInGroup = kids.filter(kid =>
    userMembership?.kidIds?.includes(kid.id)
  );

  console.log('ContributeScreen Debug:');
  console.log('- GroupId:', groupId);
  console.log('- ColectaId:', colectaId);
  console.log('- Found colecta:', !!colecta);
  console.log('- Colecta title:', colecta?.motivo);
  console.log('- User kids in group:', userKidsInGroup.length);

  useEffect(() => {
    if (userKidsInGroup.length === 1) {
      setSelectedKidId(userKidsInGroup[0].id);
    }
  }, [userKidsInGroup]);

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      setProofImage(result.assets[0].uri);
    }
  };

  const removeProofImage = () => {
    setProofImage(null);
  };

  const handleContribute = async () => {
    // Reset errors
    setErrors({});

    if (!selectedKidId) {
      setErrors({kid: 'Por favor selecciona un Qid'});
      return;
    }

    if (!amount) {
      setErrors({amount: 'Por favor ingresa un monto'});
      return;
    }

    const amountNumber = parseFloat(amount);
    if (isNaN(amountNumber) || amountNumber <= 0) {
      setErrors({amount: 'Por favor ingresa un monto válido'});
      return;
    }

    setIsLoading(true);
    try {
      await contributeToColecta(
        colectaId as string,
        selectedKidId,
        amountNumber,
        proofImage || undefined
      );

      Alert.alert(
        'Éxito',
        'Contribución registrada correctamente',
        [
          {
            text: 'OK',
            onPress: () => router.back()
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'No se pudo registrar la contribución');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-AR', {
      style: 'currency',
      currency: 'ARS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (!colecta) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: 'Contribuir',
            headerBackTitle: 'Atrás',
          }}
        />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>Colecta no encontrada</Text>
          <Button
            title="Volver"
            onPress={() => router.back()}
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Contribuir',
          headerBackTitle: 'Atrás',
        }}
      />

      <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
            {/* Colecta Info */}
            <Card variant="elevated" style={styles.colectaCard}>
              <Text style={styles.colectaTitle}>{colecta.motivo}</Text>
              {colecta.descripcion && (
                <Text style={styles.colectaDescription}>{colecta.descripcion}</Text>
              )}
              <Text style={styles.suggestedAmount}>
                Monto sugerido: {formatCurrency(colecta.baseAmount)}
              </Text>
            </Card>

            {/* Contribution Form */}
            <Card variant="elevated" style={styles.formCard}>
              {userKidsInGroup.length > 0 ? (
                <>
                  <Text style={styles.label}>Selecciona un Qid:</Text>
                  <View style={styles.kidSelector}>
                    {userKidsInGroup.map(kid => (
                      <TouchableOpacity
                        key={kid.id}
                        style={[
                          styles.kidOption,
                          selectedKidId === kid.id && styles.selectedKidOption
                        ]}
                        onPress={() => {
                          setSelectedKidId(kid.id);
                          setErrors({...errors, kid: ''});
                        }}
                      >
                        <Text
                          style={[
                            styles.kidOptionText,
                            selectedKidId === kid.id && styles.selectedKidOptionText
                          ]}
                        >
                          {kid.fullName}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                  {errors.kid && <Text style={styles.errorText}>{errors.kid}</Text>}

                  <Input
                    label="Monto"
                    placeholder="Ingresa el monto"
                    value={amount}
                    onChangeText={(text) => {
                      setAmount(text);
                      setErrors({...errors, amount: ''});
                    }}
                    keyboardType="numeric"
                    leftIcon={<DollarSign size={20} color={colors.textSecondary} />}
                    error={errors.amount}
                  />

                  <Text style={styles.label}>Comprobante de pago (opcional):</Text>
                  <TouchableOpacity
                    style={styles.uploadContainer}
                    onPress={pickImage}
                  >
                    {proofImage ? (
                      <View style={styles.proofImageContainer}>
                        <Image source={{ uri: proofImage }} style={styles.proofImage} />
                        <TouchableOpacity
                          style={styles.removeImageButton}
                          onPress={removeProofImage}
                        >
                          <X size={20} color={colors.error} />
                        </TouchableOpacity>
                      </View>
                    ) : (
                      <View style={styles.uploadPlaceholder}>
                        <Upload size={24} color={colors.textSecondary} />
                        <Text style={styles.uploadText}>Subir comprobante</Text>
                      </View>
                    )}
                  </TouchableOpacity>

                  <Button
                    title="Registrar Contribución"
                    onPress={handleContribute}
                    loading={isLoading}
                    style={styles.contributeButton}
                  />
                </>
              ) : (
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    No tienes Qids en este grupo para contribuir
                  </Text>
                </View>
              )}
            </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  scrollContent: {
    padding: 16,
  },
  colectaCard: {
    marginBottom: 16,
    padding: 16,
  },
  colectaTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  colectaDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  suggestedAmount: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  formCard: {
    padding: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  kidSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  kidOption: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.cardBackground,
  },
  selectedKidOption: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight,
  },
  kidOptionText: {
    fontSize: 14,
    color: colors.text,
  },
  selectedKidOptionText: {
    color: colors.primary,
    fontWeight: '500',
  },
  uploadContainer: {
    marginBottom: 16,
  },
  uploadPlaceholder: {
    height: 80,
    borderWidth: 1,
    borderColor: colors.border,
    borderStyle: 'dashed',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  uploadText: {
    marginTop: 8,
    fontSize: 14,
    color: colors.textSecondary,
  },
  proofImageContainer: {
    position: 'relative',
    width: '100%',
    height: 120,
    borderRadius: 8,
    overflow: 'hidden',
  },
  proofImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  contributeButton: {
    marginTop: 8,
  },
  emptyContainer: {
    padding: 16,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
    marginTop: 4,
    marginBottom: 16,
  },
  backButton: {
    minWidth: 120,
  },
});
