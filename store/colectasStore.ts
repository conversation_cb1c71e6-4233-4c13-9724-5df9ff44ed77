import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { v4 as uuidv4 } from 'uuid';
import { Colecta, ColectaStatus, Contribution } from '@/types';
import { ColectasService } from '@/services/colectasService';
import { useAuthStore } from './authStore';
import { handleAuthError, getAuthErrorMessage } from '@/utils/authErrorHandler';

interface ColectasState {
  colectas: Colecta[];
  contributions: Contribution[];
  isLoading: boolean;
  error: string | null;

  // Actions
  getColectasByGroupId: (groupId: string) => Promise<void>;
  getColectaById: (id: string) => Colecta | null;
  getContributionsByColectaId: (colectaId: string) => Promise<void>;
  createColecta: (
    groupId: string,
    motivo: string,
    descripcion: string,
    fechaFin: string,
    baseAmount: number,
    targetAmount: number,
    accountInfo: string,
    titularCuenta: string,
    cuitCuil: string,
    aliasCbu: string
  ) => Promise<Colecta>;
  contributeToColecta: (
    colectaId: string,
    kidId: string,
    amount: number,
    proofImage?: string,
    notes?: string
  ) => Promise<void>;
  dismissColecta: (colectaId: string) => Promise<void>;
  clearError: () => void;
}

export const useColectasStore = create<ColectasState>()(
  persist(
    (set, get) => ({
      colectas: [],
      contributions: [],
      isLoading: false,
      error: null,

      getColectasByGroupId: async (groupId: string) => {
        set({ isLoading: true, error: null });

        try {
          console.log('Fetching colectas for group:', groupId);

          // Get colectas from Supabase
          const colectas = await ColectasService.getColectasByGroupId(groupId);

          console.log(`Fetched ${colectas.length} colectas`);

          // Get contributions for these colectas
          const colectaIds = colectas.map(c => c.id);
          let contributions: Contribution[] = [];

          if (colectaIds.length > 0) {
            console.log('Fetching contributions for colectas:', colectaIds);
            contributions = await ColectasService.getContributionsByColectaIds(colectaIds);
            console.log(`Fetched ${contributions.length} contributions`);
          }

          // Update the store with the fetched data
          set(state => {
            // Filter out existing colectas for this group
            const filteredColectas = state.colectas ?
              state.colectas.filter(c => c.groupId !== groupId) : [];

            // Filter out existing contributions for these colectas
            const filteredContributions = state.contributions ?
              state.contributions.filter(c => !colectaIds.includes(c.colectaId)) : [];

            return {
              colectas: [...filteredColectas, ...colectas],
              contributions: [...filteredContributions, ...contributions],
              isLoading: false
            };
          });
        } catch (error) {
          console.error('Error fetching colectas:', error);
          set({
            error: error instanceof Error ? error.message : 'No se pudieron cargar las colectas. Intenta nuevamente.',
            isLoading: false
          });
        }
      },

      getColectaById: (id: string) => {
        const { colectas } = get();
        if (!colectas) return null;
        return colectas.find(colecta => colecta.id === id) || null;
      },

      getContributionsByColectaId: async (colectaId: string) => {
        set({ isLoading: true, error: null });

        try {
          console.log('Fetching contributions for colecta:', colectaId);

          // Get contributions from the service
          const contributions = await ColectasService.getContributionsByColectaId(colectaId);

          console.log(`Fetched ${contributions.length} contributions`);

          // Update the store with the fetched contributions
          set(state => ({
            contributions: [
              ...state.contributions.filter(c => c.colectaId !== colectaId),
              ...contributions
            ],
            isLoading: false
          }));
        } catch (error) {
          console.error('Error fetching contributions:', error);
          set({
            error: error instanceof Error ? error.message : 'No se pudieron cargar las contribuciones. Intenta nuevamente.',
            isLoading: false
          });
        }
      },

      createColecta: async (
        groupId: string,
        motivo: string,
        descripcion: string,
        fechaFin: string,
        baseAmount: number,
        targetAmount: number,
        accountInfo: string,
        titularCuenta: string,
        cuitCuil: string,
        aliasCbu: string
      ) => {
        set({ isLoading: true, error: null });

        try {
          console.log('Creating colecta for group:', groupId);

          // Get auth state with detailed logging
          const authState = useAuthStore.getState();
          console.log('Auth state:', {
            isAuthenticated: authState.isAuthenticated,
            currentUser: authState.currentUser,
            hasCurrentUser: !!authState.currentUser,
            currentUserId: authState.currentUser?.id
          });

          if (!authState.currentUser) {
            console.error('No current user found in auth state');
            throw new Error('Usuario no autenticado. Por favor inicia sesión nuevamente.');
          }

          if (!authState.currentUser.id) {
            console.error('Current user has no ID');
            throw new Error('Error de autenticación. Por favor inicia sesión nuevamente.');
          }

          console.log('Current user ID:', authState.currentUser.id);

          // Create colecta using the service
          const newColecta = await ColectasService.createColecta(
            groupId,
            authState.currentUser.id,
            {
              motivo,
              descripcion,
              fechaFin,
              baseAmount,
              targetAmount,
              accountInfo,
              titularCuenta,
              cuitCuil,
              aliasCbu
            }
          );

          console.log('Colecta created successfully:', newColecta);

          // Update the store with the new colecta
          set(state => ({
            colectas: [newColecta, ...(state.colectas || [])],
            isLoading: false
          }));

          return newColecta;
        } catch (error) {
          console.error('Error creating colecta:', error);

          // Handle authentication errors
          await handleAuthError(error);

          const errorMessage = getAuthErrorMessage(error);
          set({
            error: errorMessage,
            isLoading: false
          });
          throw error;
        }
      },

      contributeToColecta: async (
        colectaId: string,
        kidId: string,
        amount: number,
        proofImage?: string,
        notes?: string
      ) => {
        set({ isLoading: true, error: null });

        try {
          console.log('Contributing to colecta:', colectaId);

          const colecta = get().getColectaById(colectaId);
          if (!colecta) {
            throw new Error('Colecta no encontrada');
          }

          // Check if this kid already contributed to this colecta
          const { contributions } = get();
          const existingContribution = contributions && contributions.find(
            c => c.colectaId === colectaId && c.kidId === kidId
          );

          if (existingContribution) {
            throw new Error('Ya has contribuido a esta colecta');
          }

          // Get current user
          const { currentUser } = useAuthStore.getState();
          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          // Create contribution using the service
          const newContribution = await ColectasService.createContribution(
            colectaId,
            kidId,
            currentUser.id,
            amount,
            'Transferencia', // Default payment method
            undefined, // No payment reference
            proofImage, // Proof of payment image
            notes // Notes
          );

          console.log('Contribution created successfully:', newContribution);

          // Fetch updated contributions
          await get().getContributionsByColectaId(colectaId);

          // Fetch updated colecta
          await get().getColectasByGroupId(colecta.groupId);

          set({ isLoading: false });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'No se pudo registrar la contribución. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      dismissColecta: async (colectaId: string) => {
        set({ isLoading: true, error: null });

        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));

          const colecta = get().getColectaById(colectaId);
          if (!colecta) {
            throw new Error('Colecta no encontrada');
          }

          // Update colecta status
          const updatedColecta: Colecta = {
            ...colecta,
            status: 'DISMISSED' as ColectaStatus,
          };

          set(state => ({
            colectas: state.colectas ? state.colectas.map(c => c.id === colectaId ? updatedColecta : c) : [],
            isLoading: false
          }));
        } catch (error) {
          set({
            error: 'No se pudo descartar la colecta. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // Clear all data in the store
      clearStore: () => {
        set({ colectas: [], contributions: [], isLoading: false, error: null });
      },
    }),
    {
      name: 'colectas-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);