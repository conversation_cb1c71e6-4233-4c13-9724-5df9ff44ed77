-- Fix null values in colectas table
-- Update existing null values to 0
UPDATE public.colectas 
SET total_collected = 0 
WHERE total_collected IS NULL;

UPDATE public.colectas 
SET total_contributions = 0 
WHERE total_contributions IS NULL;

-- Set default values for future inserts
ALTER TABLE public.colectas 
ALTER COLUMN total_collected SET DEFAULT 0;

ALTER TABLE public.colectas 
ALTER COLUMN total_contributions SET DEFAULT 0;

-- Add NOT NULL constraints to prevent future null values
ALTER TABLE public.colectas 
ALTER COLUMN total_collected SET NOT NULL;

ALTER TABLE public.colectas 
ALTER COLUMN total_contributions SET NOT NULL;
