import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { DollarSign, Clock, CheckCircle, XCircle } from 'lucide-react-native';
import { Card } from './Card';
import { Badge } from './Badge';
import { colors } from '@/constants/colors';
import { ContributionStatus } from '@/types';

interface ContributionItemProps {
  kidName: string;
  parentName: string;
  amount: number;
  status: ContributionStatus | string;
  date?: string;
  createdAt?: string;
  proofUrl?: string;
  notes?: string;
  onProofPress?: () => void;
}

export const ContributionItem: React.FC<ContributionItemProps> = ({
  kidName,
  parentName,
  amount,
  status,
  date,
  createdAt,
  proofUrl,
  notes,
  onProofPress,
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-AR', {
      style: 'currency',
      currency: 'ARS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = () => {
    const dateString = date || createdAt;
    if (!dateString) return '';

    try {
      const dateObj = new Date(dateString);
      return dateObj.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: 'short',
      });
    } catch (error) {
      console.error('Invalid date:', dateString);
      return '';
    }
  };

  const getStatusIcon = () => {
    const normalizedStatus = status.toUpperCase();

    switch (normalizedStatus) {
      case 'REGISTRADA':
        return <CheckCircle size={16} color={colors.success} />;
      case 'DESCARTADA':
        return <XCircle size={16} color={colors.error} />;
      default:
        return <Clock size={16} color={colors.warning} />;
    }
  };

  const getStatusLabel = () => {
    const normalizedStatus = status.toUpperCase();

    switch (normalizedStatus) {
      case 'REGISTRADA':
        return 'Registrada';
      case 'DESCARTADA':
        return 'Descartada';
      default:
        return 'Pendiente';
    }
  };

  const getStatusVariant = (): 'warning' | 'success' | 'error' => {
    const normalizedStatus = status.toUpperCase();

    switch (normalizedStatus) {
      case 'REGISTRADA':
        return 'success';
      case 'DESCARTADA':
        return 'error';
      default:
        return 'warning';
    }
  };

  return (
    <Card variant="outlined" style={styles.card}>
      <View style={styles.header}>
        <View style={styles.namesContainer}>
          <Text style={styles.name}>{kidName}</Text>
          <Text style={styles.parentName}>{parentName}</Text>
          {notes && (
            <Text style={styles.notes}>"{notes}"</Text>
          )}
        </View>
        <Badge
          label={getStatusLabel()}
          variant={getStatusVariant()}
        />
      </View>

      <View style={styles.details}>
        <View style={styles.amountContainer}>
          <Text style={styles.amount}>{formatCurrency(amount)}</Text>
        </View>

        <Text style={styles.date}>{formatDate()}</Text>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
    padding: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  namesContainer: {
    flex: 1,
    marginRight: 8,
  },
  name: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  parentName: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 2,
  },
  notes: {
    fontSize: 11,
    color: colors.textSecondary,
    marginTop: 2,
    fontStyle: 'italic',
  },
  details: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  amount: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  date: {
    fontSize: 14,
    color: colors.textSecondary,
  },
});